import { Button } from '@/components/ui/button';
import { FileText, Bot, Upload, Sparkles } from 'lucide-react';

export const Header = () => {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-16 items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="p-2 rounded-lg bg-gradient-to-br from-primary to-primary-glow">
            <FileText className="h-6 w-6 text-primary-foreground" />
          </div>
          <div>
            <h1 className="text-xl font-bold bg-gradient-to-r from-primary to-primary-glow bg-clip-text text-transparent">
              DocuChat AI
            </h1>
            <p className="text-xs text-muted-foreground">Upload • Analyze • Chat</p>
          </div>
        </div>
        
        <nav className="flex items-center space-x-4">
          <Button variant="ghost" size="sm" className="hidden md:flex">
            <Upload className="h-4 w-4 mr-2" />
            Upload
          </Button>
          <Button variant="ghost" size="sm" className="hidden md:flex">
            <Bot className="h-4 w-4 mr-2" />
            Chat
          </Button>
          <Button variant="outline" size="sm" className="bg-gradient-to-r from-primary/10 to-primary-glow/10 border-primary/20">
            <Sparkles className="h-4 w-4 mr-2" />
            Pro
          </Button>
        </nav>
      </div>
    </header>
  );
};