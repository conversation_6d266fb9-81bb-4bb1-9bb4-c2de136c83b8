import { Header } from '@/components/Header';
import { FileUpload } from '@/components/FileUpload';
import { ChatBot } from '@/components/ChatBot';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, Bot, Upload, Zap, Shield, Clock } from 'lucide-react';

const Index = () => {
  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Hero Section */}
      <section className="relative py-20 px-4">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-primary-glow/5"></div>
        <div className="container mx-auto text-center relative">
          <div className="inline-flex items-center space-x-2 bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-6">
            <Zap className="h-4 w-4" />
            <span>AI-Powered Document Analysis</span>
          </div>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
            Upload, Analyze, and Chat with Your Documents
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Transform your PDF documents into intelligent conversations. Upload your files and let our AI assistant help you extract insights, answer questions, and analyze content.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="gradient" size="lg" className="text-lg px-8">
              <Upload className="h-5 w-5 mr-2" />
              Start Uploading
            </Button>
            <Button variant="outline" size="lg" className="text-lg px-8">
              <Bot className="h-5 w-5 mr-2" />
              Try Demo
            </Button>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-3 gap-8 mb-16">
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="mx-auto p-3 bg-primary/10 rounded-full w-fit">
                  <Upload className="h-8 w-8 text-primary" />
                </div>
                <CardTitle>Easy Upload</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Simply drag and drop your PDF files or click to browse. We support multiple file formats.
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="mx-auto p-3 bg-primary/10 rounded-full w-fit">
                  <Bot className="h-8 w-8 text-primary" />
                </div>
                <CardTitle>AI Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Our advanced AI processes your documents and provides intelligent insights and summaries.
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="mx-auto p-3 bg-primary/10 rounded-full w-fit">
                  <Shield className="h-8 w-8 text-primary" />
                </div>
                <CardTitle>Secure & Private</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  Your documents are processed securely with enterprise-grade encryption and privacy protection.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="grid lg:grid-cols-2 gap-8">
            {/* File Upload Section */}
            <div>
              <div className="mb-6">
                <h2 className="text-3xl font-bold mb-4">Upload Your Documents</h2>
                <p className="text-muted-foreground">
                  Upload PDF files and let our AI assistant analyze them for you. Supported formats include PDF documents up to 10MB each.
                </p>
              </div>
              <FileUpload />
            </div>

            {/* Chat Section */}
            <div>
              <div className="mb-6">
                <h2 className="text-3xl font-bold mb-4">Chat with Your Documents</h2>
                <p className="text-muted-foreground">
                  Ask questions about your uploaded documents and get intelligent responses from our AI assistant.
                </p>
              </div>
              <ChatBot />
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t py-12 px-4">
        <div className="container mx-auto text-center">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <div className="p-2 rounded-lg bg-gradient-to-br from-primary to-primary-glow">
              <FileText className="h-5 w-5 text-primary-foreground" />
            </div>
            <span className="text-lg font-bold">DocuChat AI</span>
          </div>
          <p className="text-muted-foreground">
            Empowering document analysis through AI technology
          </p>
        </div>
      </footer>
    </div>
  );
};

export default Index;
