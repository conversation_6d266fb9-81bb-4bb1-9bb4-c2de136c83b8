import { useState, useCallback, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Upload, File, CheckCircle, X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface UploadedFile {
  id: string;
  name: string;
  filename?: string;
  size: number;
  type: string;
  uploadedAt: Date;
}

export const FileUpload = () => {
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  // Load existing files on component mount
  const loadExistingFiles = useCallback(async () => {
    try {
      const response = await fetch('/api/files');
      if (response.ok) {
        const result = await response.json();
        const files: UploadedFile[] = result.files.map((file: any) => ({
          id: file.id,
          name: file.name,
          filename: file.filename,
          size: file.size,
          type: file.type,
          uploadedAt: new Date(file.uploadedAt)
        }));
        setUploadedFiles(files);
      }
    } catch (error) {
      console.error('Error loading files:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadExistingFiles();
  }, [loadExistingFiles]);

  const handleFileUpload = useCallback(async (files: FileList) => {
    const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
    const MAX_FILES = 10;

    const pdfFiles = Array.from(files).filter(file =>
      file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')
    );

    if (pdfFiles.length === 0) {
      toast({
        title: "Invalid file type",
        description: "Only PDF files are accepted.",
        variant: "destructive",
      });
      return;
    }

    if (pdfFiles.length > MAX_FILES) {
      toast({
        title: "Too many files",
        description: `Maximum ${MAX_FILES} files can be uploaded at once.`,
        variant: "destructive",
      });
      return;
    }

    const oversizedFiles = pdfFiles.filter(file => file.size > MAX_FILE_SIZE);
    if (oversizedFiles.length > 0) {
      toast({
        title: "File too large",
        description: `${oversizedFiles.length} file(s) exceed the 10MB limit: ${oversizedFiles.map(f => f.name).join(', ')}`,
        variant: "destructive",
      });
      return;
    }

    // Check if adding these files would exceed current total
    if (uploadedFiles.length + pdfFiles.length > MAX_FILES) {
      toast({
        title: "Upload limit exceeded",
        description: `You can only have ${MAX_FILES} files total. Currently have ${uploadedFiles.length} files.`,
        variant: "destructive",
      });
      return;
    }

    setIsUploading(true);

    try {
      const formData = new FormData();
      pdfFiles.forEach(file => {
        formData.append('files', file);
      });

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Upload failed');
      }

      const result = await response.json();

      const newFiles: UploadedFile[] = result.files.map((file: any) => ({
        id: file.id,
        name: file.name,
        filename: file.filename,
        size: file.size,
        type: file.type,
        uploadedAt: new Date(file.uploadedAt)
      }));

      setUploadedFiles(prev => [...prev, ...newFiles]);

      toast({
        title: "Files uploaded successfully",
        description: `${pdfFiles.length} file(s) have been uploaded to the server.`,
      });
    } catch (error) {
      console.error('Upload error:', error);
      toast({
        title: "Upload failed",
        description: error instanceof Error ? error.message : "Failed to upload files. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsUploading(false);
    }
  }, [toast]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    const files = e.dataTransfer.files;
    handleFileUpload(files);
  }, [handleFileUpload]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFileUpload(e.target.files);
    }
  }, [handleFileUpload]);

  const removeFile = useCallback(async (id: string) => {
    const fileToRemove = uploadedFiles.find(file => file.id === id);
    if (!fileToRemove) return;

    try {
      const filename = fileToRemove.filename || fileToRemove.name;
      const response = await fetch(`/api/files/${encodeURIComponent(filename)}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete file');
      }

      setUploadedFiles(prev => prev.filter(file => file.id !== id));
      toast({
        title: "File removed",
        description: "File has been removed from the server.",
      });
    } catch (error) {
      console.error('Delete error:', error);
      toast({
        title: "Delete failed",
        description: error instanceof Error ? error.message : "Failed to delete file. Please try again.",
        variant: "destructive",
      });
    }
  }, [uploadedFiles, toast]);

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card className="border-2 border-dashed">
          <CardContent className="p-8">
            <div className="text-center">
              <p className="text-muted-foreground">Loading existing files...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <Card className="border-2 border-dashed transition-all duration-300 hover:border-primary">
        <CardContent className="p-8">
          <div
            className={`relative rounded-lg border-2 border-dashed transition-all duration-300 p-8 text-center ${
              isDragOver
                ? 'border-primary bg-primary/5 scale-105'
                : 'border-muted-foreground/25 hover:border-primary/50'
            } ${isUploading ? 'opacity-50 pointer-events-none' : ''}`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            <div className="flex flex-col items-center justify-center space-y-4">
              <div className="p-4 rounded-full bg-primary/10">
                <Upload className="h-8 w-8 text-primary" />
              </div>
              <div>
                <p className="text-lg font-medium">
                  {isUploading ? 'Uploading files...' : 'Drop your PDF files here'}
                </p>
                <p className="text-muted-foreground">
                  {isUploading ? 'Please wait while files are being uploaded' : 'or click to browse'}
                </p>
              </div>
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <span>Supported formats: PDF</span>
              </div>
              <input
                type="file"
                accept=".pdf,application/pdf"
                multiple
                onChange={handleFileInputChange}
                disabled={isUploading}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer disabled:cursor-not-allowed"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {uploadedFiles.length > 0 && (
        <Card>
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center">
              <CheckCircle className="h-5 w-5 text-green-500 mr-2" />
              Uploaded Files ({uploadedFiles.length})
            </h3>
            <div className="space-y-3">
              {uploadedFiles.map((file) => (
                <div
                  key={file.id}
                  className="flex items-center justify-between p-3 bg-secondary rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <File className="h-5 w-5 text-primary" />
                    <div>
                      <p className="font-medium">{file.name}</p>
                      <p className="text-sm text-muted-foreground">
                        {formatFileSize(file.size)} • Uploaded {file.uploadedAt.toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFile(file.id)}
                    className="text-muted-foreground hover:text-destructive"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};